#!/usr/bin/env node

import * as path from "path";
import select from "@inquirer/select";
import checkbox from "@inquirer/checkbox";
import { BuildTarget } from "./common/types";
import { ConfigUtils } from "./common/config-utils";
import { AugmentTarget } from "./targets/augment";
import { CursorTarget } from "./targets/cursor";
import { CopilotTarget } from "./targets/copilot";

/**
 * 统一规则构建器
 * 从根目录 rules/ 文件夹读取规则文件，支持构建到多个目标：Augment (.augment/rules/*.md)、Cursor (.cursor/rules/*.mdc) 和 Copilot (.github/instructions/*.instructions.md)
 */
class RuleBuilder {
  private sourceDir: string;
  private workDir: string;
  private projectRoot: string;
  private configUtils: ConfigUtils;

  constructor() {
    this.projectRoot = ConfigUtils.getProjectRoot();
    this.workDir = ConfigUtils.getWorkDir();
    this.sourceDir = path.join(this.projectRoot, "rules");
    this.configUtils = new ConfigUtils(this.projectRoot);
  }

  /**
   * 主构建流程
   */
  async build(): Promise<void> {
    console.log("🚀 开始构建规则目录...\n");

    try {
      // 1. 选择构建目标
      const targets = await this.selectTargets();

      // 2. 构建到当前项目
      await this.buildToCurrentProject(targets);

      // 3. 询问是否部署到其他项目
      const deployToOther = await this.askDeployToOther();

      if (deployToOther) {
        // 4. 扫描同级目录
        const projects = ConfigUtils.scanProjects();

        // 5. 选择目标项目
        const targetProject = await this.selectTargetProject(projects);

        // 6. 部署到目标项目
        await this.deployToProject(targetProject, targets);
      }

      console.log("\n🎉 所有构建任务完成！");
    } catch (error) {
      console.error("❌ 构建失败:", (error as Error).message);
      process.exit(1);
    }
  }

  /**
   * 选择构建目标
   */
  async selectTargets(): Promise<BuildTarget[]> {
    const targets = await checkbox({
      message: "请选择构建目标: (默认已选中 Augment 和 Cursor)",
      choices: [
        { name: "Augment (.augment/rules/*.md)", value: "augment", checked: true },
        { name: "Cursor (.cursor/rules/*.mdc)", value: "cursor", checked: true },
        { name: "Copilot (.github/instructions/*.instructions.md)", value: "copilot", checked: true },
      ],
      validate: (choices) => {
        if (choices.length === 0) {
          return "请至少选择一个构建目标";
        }
        return true;
      },
    });

    console.log(`🎯 选择目标: ${targets.join(", ")}\n`);
    return targets as BuildTarget[];
  }

  /**
   * 构建到当前项目
   */
  async buildToCurrentProject(targets: BuildTarget[]): Promise<void> {
    console.log("📦 步骤 1: 构建到当前项目...");

    for (const target of targets) {
      await this.buildTarget(target, this.projectRoot, true);
    }

    console.log("✅ 当前项目构建完成！\n");
  }

  /**
   * 询问是否部署到其他项目
   */
  async askDeployToOther(): Promise<boolean> {
    const deploy = await select({
      message: "是否需要部署到其他项目？",
      choices: [
        { name: "是，选择其他项目", value: true },
        { name: "否，仅构建当前项目", value: false },
      ],
      default: true,
    });

    return deploy;
  }

  /**
   * 选择目标项目
   */
  async selectTargetProject(projects: string[]): Promise<string> {
    const lastConfig = this.configUtils.loadConfig();

    const choices = projects.map((project) => ({
      name: project,
      value: project,
    }));

    const targetProject = await select({
      message: "请选择目标项目:",
      choices: choices,
      default: lastConfig.lastProject || projects[0],
    });

    console.log(`🎯 选择项目: ${targetProject}\n`);
    return targetProject;
  }

  /**
   * 部署到指定项目
   */
  async deployToProject(targetProject: string, targets: BuildTarget[]): Promise<void> {
    console.log(`📦 步骤 2: 部署到项目 ${targetProject}...`);

    const targetProjectPath = path.join(this.workDir, targetProject);

    for (const target of targets) {
      await this.buildTarget(target, targetProjectPath, false);
    }

    // 保存配置
    this.configUtils.saveConfig(targetProject, targets);

    console.log("✅ 项目部署完成！");
  }

  /**
   * 构建指定目标
   */
  private async buildTarget(
    target: BuildTarget,
    projectPath: string,
    isCurrentProject: boolean
  ): Promise<void> {
    switch (target) {
      case "augment":
        await AugmentTarget.buildToProject(this.sourceDir, projectPath, isCurrentProject);
        break;
      case "cursor":
        await CursorTarget.buildToProject(this.sourceDir, projectPath, isCurrentProject);
        break;
      case "copilot":
        await CopilotTarget.buildToProject(this.sourceDir, projectPath, isCurrentProject);
        break;
      default:
        throw new Error(`未知的构建目标: ${target}`);
    }
  }
}

// 执行构建
if (require.main === module) {
  const builder = new RuleBuilder();
  builder.build().catch(console.error);
}

export default RuleBuilder;
