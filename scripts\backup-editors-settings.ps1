﻿# 编辑器设置备份脚本
# 备份Cursor和VSCode的设置、快捷键、代码片段、扩展等完整配置

param(
    [string]$BackupPath = "backup"
)

# 设置控制台编码为UTF-8以支持中文输出
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 智能获取扩展列表函数
function Get-ValidExtensions {
    param(
        [string]$EditorName,
        [string]$ExtensionsPath,
        [string]$CliCommand
    )
    
    $validExtensions = @()
    $getMethod = "Unknown"
    
    # 只对VSCode使用CLI方法
    if ($EditorName -eq "VSCode") {
        # 方法1：使用CLI命令获取扩展列表（仅VSCode）
        try {
            Write-Host "🔍 [$EditorName] 使用CLI获取扩展列表..." -ForegroundColor Gray
            $cliResult = & $CliCommand --list-extensions --show-versions 2>$null
            if ($LASTEXITCODE -eq 0 -and $cliResult) {
                $getMethod = "CLI"
                foreach ($line in $cliResult) {
                    if ($line -match '^(.+?)@(.+)$') {
                        $extensionId = $matches[1]
                        $version = $matches[2]
                        
                        # 分割publisher和name
                        if ($extensionId -match '^(.+?)\.(.+)$') {
                            $validExtensions += @{
                                name = $matches[2]
                                displayName = $matches[2]
                                version = $version
                                publisher = $matches[1]
                                description = "Retrieved via CLI"
                                extensionId = $extensionId
                                source = "CLI"
                            }
                        }
                    }
                }
                Write-Host "✅ [$EditorName] CLI获取成功: $($validExtensions.Count) 个扩展" -ForegroundColor Green
            } else {
                throw "CLI命令执行失败或无结果"
            }
        } catch {
            Write-Host "⚠️  [$EditorName] CLI获取失败: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-Host "🔄 [$EditorName] 回退到目录扫描方式..." -ForegroundColor Gray
        }
    }
    
    # 如果CLI失败或者是Cursor，使用目录扫描方式
    if ($validExtensions.Count -eq 0 -and (Test-Path $ExtensionsPath)) {
        Write-Host "🔍 [$EditorName] 使用目录扫描方式..." -ForegroundColor Gray
        $getMethod = "Directory"
        $extensionDirs = Get-ChildItem -Path $ExtensionsPath -Directory
        $totalDirs = $extensionDirs.Count
        $validCount = 0
        $invalidCount = 0
        
        Write-Host "📁 [$EditorName] 发现 $totalDirs 个扩展目录，开始验证..." -ForegroundColor Gray
        
        foreach ($extDir in $extensionDirs) {
            $packageJsonPath = Join-Path $extDir.FullName "package.json"
            
            # 验证扩展完整性
            if (Test-Path $packageJsonPath) {
                try {
                    $packageJson = Get-Content $packageJsonPath -Raw -Encoding UTF8 | ConvertFrom-Json
                    
                    # 基本信息验证
                    $publisher = if ($packageJson.publisher) { $packageJson.publisher } else { "Unknown" }
                    $name = if ($packageJson.name) { $packageJson.name } else { "Unknown" }
                    $version = if ($packageJson.version) { $packageJson.version } else { "Unknown" }
                    $displayName = if ($packageJson.displayName) { $packageJson.displayName } else { $name }
                    
                    # 针对Cursor进行增强验证，VSCode目录扫描时使用基本验证
                    $isValid = $true
                    
                    if ($EditorName -eq "Cursor") {
                        # Cursor使用适度的验证规则
                        # 1. 检查版本号格式（允许更灵活的格式）
                        if ($version -eq "Unknown" -or $version -eq "") {
                            $isValid = $false
                        }
                        
                        # 2. 检查基本文件存在（放宽要求）
                        $hasPackageJson = Test-Path $packageJsonPath
                        $hasMainContent = (Get-ChildItem -Path $extDir.FullName -File -Recurse).Count -gt 0
                        if (-not $hasPackageJson -or -not $hasMainContent) {
                            $isValid = $false
                        }
                        
                        # 3. 排除明显的临时目录
                        if ($extDir.Name -like "*test*" -or $extDir.Name -like "*tmp*" -or $extDir.Name -like ".*") {
                            $isValid = $false
                        }
                    }
                    
                    if ($isValid) {
                        # 如果解析失败，尝试从目录名提取信息
                        if ($publisher -eq "Unknown" -or $name -eq "Unknown" -or $version -eq "Unknown") {
                            # 目录名格式通常是：publisher.name-version
                            if ($extDir.Name -match '^(.+?)\.(.+?)-(.+)$') {
                                if ($publisher -eq "Unknown") { $publisher = $matches[1] }
                                if ($name -eq "Unknown") { $name = $matches[2] }
                                if ($version -eq "Unknown") { $version = $matches[3] }
                            }
                        }
                        
                        $validExtensions += @{
                            name = $name
                            displayName = $displayName
                            version = $version
                            publisher = $publisher
                            description = if ($packageJson.description) { $packageJson.description } else { "" }
                            extensionId = "$publisher.$name"
                            source = if ($EditorName -eq "Cursor") { "Directory-Enhanced" } else { "Directory-Basic" }
                        }
                        $validCount++
                    } else {
                        $invalidCount++
                        Write-Host "⚠️  [$EditorName] 跳过扩展: $($extDir.Name) (验证失败)" -ForegroundColor DarkGray
                    }
                    
                } catch {
                    # 忽略解析失败的扩展
                    $invalidCount++
                    Write-Host "⚠️  [$EditorName] 跳过扩展: $($extDir.Name) (解析失败)" -ForegroundColor DarkGray
                    continue
                }
            }
        }
        Write-Host "✅ [$EditorName] 目录扫描完成: $($validExtensions.Count) 个有效扩展 (总计: $totalDirs, 有效: $validCount, 无效: $invalidCount)" -ForegroundColor Green
    }
    
    return @{
        Extensions = $validExtensions
        Method = $getMethod
        Count = $validExtensions.Count
    }
}

Write-Host "🚀 编辑器配置备份工具" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Gray

# 检查编辑器配置目录是否存在
$cursorUserPath = "$env:APPDATA\Cursor\User"
$vscodeUserPath = "$env:APPDATA\Code\User"
$cursorExists = Test-Path $cursorUserPath
$vscodeExists = Test-Path $vscodeUserPath

# 显示可用的编辑器
Write-Host "`n📋 检测到的编辑器:" -ForegroundColor Cyan
if ($cursorExists) {
    Write-Host "   ✅ Cursor - 配置可用" -ForegroundColor Green
} else {
    Write-Host "   ❌ Cursor - 未安装或配置不存在" -ForegroundColor Red
}

if ($vscodeExists) {
    Write-Host "   ✅ VSCode - 配置可用" -ForegroundColor Green
} else {
    Write-Host "   ❌ VSCode - 未安装或配置不存在" -ForegroundColor Red
}

if (-not $cursorExists -and -not $vscodeExists) {
    Write-Host "`n❌ 错误：未找到任何编辑器配置目录" -ForegroundColor Red
    Write-Host "请确保至少安装了Cursor或VSCode中的一个。" -ForegroundColor Yellow
    exit 1
}

# 交互式选择要备份的编辑器
Write-Host "`n🎯 请选择要备份的编辑器:" -ForegroundColor Yellow
Write-Host "   1. 备份所有可用编辑器 (默认)" -ForegroundColor White
if ($cursorExists) {
    Write-Host "   2. 仅备份 Cursor" -ForegroundColor White
}
if ($vscodeExists) {
    Write-Host "   3. 仅备份 VSCode" -ForegroundColor White
}

Write-Host "`n" -NoNewline
$choice = Read-Host "请输入选项 (1-3，直接回车选择默认)"

# 处理用户选择
$backupCursor = $false
$backupVSCode = $false

switch ($choice) {
    "2" { 
        if ($cursorExists) {
            $backupCursor = $true
            Write-Host "📌 已选择：仅备份 Cursor" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️  Cursor不可用，切换到备份所有可用编辑器" -ForegroundColor Yellow
            $backupCursor = $cursorExists
            $backupVSCode = $vscodeExists
        }
    }
    "3" { 
        if ($vscodeExists) {
            $backupVSCode = $true
            Write-Host "📌 已选择：仅备份 VSCode" -ForegroundColor Cyan
        } else {
            Write-Host "⚠️  VSCode不可用，切换到备份所有可用编辑器" -ForegroundColor Yellow
            $backupCursor = $cursorExists
            $backupVSCode = $vscodeExists
        }
    }
    default { 
        $backupCursor = $cursorExists
        $backupVSCode = $vscodeExists
        Write-Host "📌 已选择：备份所有可用编辑器" -ForegroundColor Cyan
    }
}

Write-Host "`n🚀 开始备份..." -ForegroundColor Green

# 获取当前时间作为备份文件夹名称
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"

# 编辑器配置文件路径（之前已定义）
# 编辑器扩展路径
$cursorExtensionsPath = "$env:USERPROFILE\.cursor\extensions"
$vscodeExtensionsPath = "$env:USERPROFILE\.vscode\extensions"

# 创建备份目录
try {
    # 为选中的编辑器创建独立的时间戳目录
    if ($backupCursor) {
        $cursorBackupDir = Join-Path $BackupPath "cursor" | Join-Path -ChildPath $timestamp
        New-Item -ItemType Directory -Path $cursorBackupDir -Force | Out-Null
        Write-Host "📁 创建Cursor备份目录: $cursorBackupDir" -ForegroundColor Gray
    }
    if ($backupVSCode) {
        $vscodeBackupDir = Join-Path $BackupPath "vscode" | Join-Path -ChildPath $timestamp
        New-Item -ItemType Directory -Path $vscodeBackupDir -Force | Out-Null
        Write-Host "📁 创建VSCode备份目录: $vscodeBackupDir" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ 错误：无法创建备份目录: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 定义要备份的文件和文件夹
$itemsToBackup = @(
    @{ Source = "settings.json"; Type = "File"; Description = "用户设置" },
    @{ Source = "keybindings.json"; Type = "File"; Description = "快捷键配置" },
    @{ Source = "snippets"; Type = "Directory"; Description = "代码片段" },
    @{ Source = "tasks.json"; Type = "File"; Description = "任务配置" },
    @{ Source = "launch.json"; Type = "File"; Description = "调试配置" }
)

# 扩展目录备份配置
$editorsExtensions = @()
if ($backupCursor -and $cursorExists) {
    $editorsExtensions += @{ 
        Name = "Cursor"; 
        UserPath = $cursorUserPath; 
        ExtensionsPath = $cursorExtensionsPath; 
        BackupDir = Join-Path $BackupPath "cursor" | Join-Path -ChildPath $timestamp;
        CliCommand = "cursor"
    }
}
if ($backupVSCode -and $vscodeExists) {
    $editorsExtensions += @{ 
        Name = "VSCode"; 
        UserPath = $vscodeUserPath; 
        ExtensionsPath = $vscodeExtensionsPath; 
        BackupDir = Join-Path $BackupPath "vscode" | Join-Path -ChildPath $timestamp;
        CliCommand = "code"
    }
}

# 检查是否有要备份的编辑器
if ($editorsExtensions.Count -eq 0) {
    Write-Host "❌ 错误：没有可备份的编辑器" -ForegroundColor Red
    exit 1
}

Write-Host "`n📋 备份计划:" -ForegroundColor Yellow
foreach ($editor in $editorsExtensions) {
    Write-Host "   • $($editor.Name) → $($editor.BackupDir)" -ForegroundColor White
}

$successCount = 0
$totalCount = 0

# 备份每个编辑器的配置文件
foreach ($editor in $editorsExtensions) {
    Write-Host "`n🔧 开始备份 $($editor.Name) 配置..." -ForegroundColor Blue
    
    foreach ($item in $itemsToBackup) {
        $sourcePath = Join-Path $editor.UserPath $item.Source
        $destPath = Join-Path $editor.BackupDir $item.Source
        $totalCount++
        
        if (Test-Path $sourcePath) {
            try {
                if ($item.Type -eq "Directory") {
                    # 复制文件夹
                    Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                    $fileCount = (Get-ChildItem -Path $sourcePath -Recurse -File).Count
                    Write-Host "✅ [$($editor.Name)] 已备份 $($item.Description): $($item.Source) ($fileCount 个文件)" -ForegroundColor Green
                } else {
                    # 复制文件
                    Copy-Item -Path $sourcePath -Destination $destPath -Force
                    $fileSize = [math]::Round((Get-Item $sourcePath).Length / 1KB, 2)
                    Write-Host "✅ [$($editor.Name)] 已备份 $($item.Description): $($item.Source) ($fileSize KB)" -ForegroundColor Green
                }
                $successCount++
            } catch {
                Write-Host "⚠️  [$($editor.Name)] 备份 $($item.Description) 失败: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "⏭️  [$($editor.Name)] 跳过 $($item.Description): 文件不存在" -ForegroundColor Gray
        }
    }
}

# 备份每个编辑器的已安装扩展列表
Write-Host "`n🔌 开始智能备份已安装扩展列表..." -ForegroundColor Blue
foreach ($editor in $editorsExtensions) {
    $totalCount++
    
    Write-Host "`n📦 处理 $($editor.Name) 扩展..." -ForegroundColor Cyan
    
    # 使用智能验证函数获取扩展列表
    $extensionResult = Get-ValidExtensions -EditorName $editor.Name -ExtensionsPath $editor.ExtensionsPath -CliCommand $editor.CliCommand
    
    if ($extensionResult.Extensions.Count -gt 0) {
        try {
            # 保存扩展列表到JSON文件
            $extensionsJsonPath = Join-Path $editor.BackupDir "extensions-list.json"
            $extensionResult.Extensions | ConvertTo-Json -Depth 3 | Out-File -FilePath $extensionsJsonPath -Encoding UTF8
            
            # 保存扩展获取信息
            $extensionInfoPath = Join-Path $editor.BackupDir "extensions-info.json"
            $extensionInfo = @{
                Method = $extensionResult.Method
                Count = $extensionResult.Count
                BackupTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                Note = if ($extensionResult.Method -eq "CLI") { "通过CLI获取，确保为真实安装的扩展" } else { "通过目录扫描获取，已进行完整性验证" }
            }
            $extensionInfo | ConvertTo-Json -Depth 2 | Out-File -FilePath $extensionInfoPath -Encoding UTF8
            
            Write-Host "✅ [$($editor.Name)] 已备份扩展列表: $($extensionResult.Count) 个扩展 ($($extensionResult.Method))" -ForegroundColor Green
            $successCount++
        } catch {
            Write-Host "⚠️  [$($editor.Name)] 备份扩展列表失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⏭️  [$($editor.Name)] 跳过扩展列表: 未找到有效扩展" -ForegroundColor Gray
    }
}

# 创建备份信息文件
$backupInfo = @{
    BackupTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    BackupChoice = if ($backupCursor -and $backupVSCode) { "All Available" } 
                   elseif ($backupCursor) { "Cursor Only" } 
                   elseif ($backupVSCode) { "VSCode Only" } 
                   else { "None" }
    Editors = @()
    SuccessCount = $successCount
    TotalCount = $totalCount
}

# 为每个编辑器创建独立的备份信息文件
foreach ($editor in $editorsExtensions) {
    $editorInfo = @{
        BackupTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        EditorName = $editor.Name
        BackupPath = $editor.BackupDir
        Version = "Unknown"
        BackupItems = $itemsToBackup | ForEach-Object { 
            $sourcePath = Join-Path $editor.UserPath $_.Source
            @{
                Name = $_.Source
                Description = $_.Description
                Exists = Test-Path $sourcePath
                Type = $_.Type
            }
        }
        ExtensionInfo = @{
            ExtensionsPath = $editor.ExtensionsPath
            Exists = Test-Path $editor.ExtensionsPath
            ExtensionCount = if (Test-Path $editor.ExtensionsPath) { 
                (Get-ChildItem -Path $editor.ExtensionsPath -Directory).Count 
            } else { 0 }
        }
    }
    
    # 为每个编辑器保存独立的备份信息
    $backupInfoPath = Join-Path $editor.BackupDir "backup-info.json"
    $editorInfo | ConvertTo-Json -Depth 3 | Out-File -FilePath $backupInfoPath -Encoding UTF8
}

Write-Host "`n📊 备份完成统计:" -ForegroundColor Magenta
Write-Host "   备份时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host "   备份位置: $BackupPath" -ForegroundColor White
Write-Host "   备份选择: $(if ($backupCursor -and $backupVSCode) { "All Available" } elseif ($backupCursor) { "Cursor Only" } elseif ($backupVSCode) { "VSCode Only" } else { "None" })" -ForegroundColor White
Write-Host "   成功备份: $successCount/$totalCount 项" -ForegroundColor White

# 显示各编辑器备份情况
Write-Host "`n📁 备份目录结构:" -ForegroundColor Cyan
foreach ($editor in $editorsExtensions) {
    Write-Host "   📂 $($editor.Name): $($editor.BackupDir)" -ForegroundColor White
}

if ($successCount -eq $totalCount) {
    Write-Host "`n🎉 所有编辑器配置文件和扩展列表备份成功！" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "`n⚠️  部分编辑器配置文件和扩展列表备份成功，请检查上述输出。" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ 备份失败，请检查错误信息。" -ForegroundColor Red
    exit 1
}


# 备份清理函数
    function Clean-OldBackups {
    param(
        [string]$BackupRootPath,
        [string]$EditorName
    )
    
    $editorBackupPath = Join-Path $BackupRootPath $EditorName.ToLower()
    
    if (-not (Test-Path $editorBackupPath)) {
        return
    }
    
    Write-Host "`n🧹 开始清理 $EditorName 历史备份..." -ForegroundColor Yellow
    
    # 获取所有备份目录，按时间排序
    $backupDirs = Get-ChildItem -Path $editorBackupPath -Directory | 
                  Where-Object { $_.Name -match '^\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}$' } |
                  Sort-Object Name -Descending
    
    if ($backupDirs.Count -eq 0) {
        Write-Host "📁 [$EditorName] 未找到历史备份目录" -ForegroundColor Gray
        return
    }
    
    Write-Host "� [$EditorName] 发现 $($backupDirs.Count) 个备份目录" -ForegroundColor Gray
    
    # 按月份分组备份
    $backupsByMonth = @{}
    foreach ($dir in $backupDirs) {
        # 从目录名提取年月 (yyyy-MM)
        if ($dir.Name -match '^(\d{4}-\d{2})') {
            $yearMonth = $matches[1]
            if (-not $backupsByMonth.ContainsKey($yearMonth)) {
                $backupsByMonth[$yearMonth] = @()
            }
            $backupsByMonth[$yearMonth] += $dir
        }
    }
    
    # 获取当前日期，计算3个月前的年月
    $currentDate = Get-Date
    $threeMonthsAgo = $currentDate.AddMonths(-3)
    $threeMonthsAgoYM = $threeMonthsAgo.ToString("yyyy-MM")
    
    $deletedCount = 0
    $keptCount = 0
    
    foreach ($yearMonth in $backupsByMonth.Keys | Sort-Object -Descending) {
        $monthBackups = $backupsByMonth[$yearMonth] | Sort-Object Name -Descending
        
        # 检查是否超过3个月
        if ($yearMonth -lt $threeMonthsAgoYM) {
            # 删除超过3个月的所有备份
            foreach ($backup in $monthBackups) {
                try {
                    Remove-Item -Path $backup.FullName -Recurse -Force
                    Write-Host "🗑️  [$EditorName] 已删除过期备份: $($backup.Name) (超过3个月)" -ForegroundColor Red
                    $deletedCount++
                } catch {
                    Write-Host "⚠️  [$EditorName] 删除备份失败: $($backup.Name) - $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
        } else {
            # 保留最近3个月内的备份，但每月只保留最新的1个
            $latestInMonth = $monthBackups[0]  # 最新的备份
            $keptCount++
            
            # 删除同月份的其他备份
            for ($i = 1; $i -lt $monthBackups.Count; $i++) {
                try {
                    Remove-Item -Path $monthBackups[$i].FullName -Recurse -Force
                    Write-Host "🗑️  [$EditorName] 已删除同月旧备份: $($monthBackups[$i].Name) (保留最新: $($latestInMonth.Name))" -ForegroundColor Red
                    $deletedCount++
                } catch {
                    Write-Host "⚠️  [$EditorName] 删除备份失败: $($monthBackups[$i].Name) - $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }
            
            Write-Host "✅ [$EditorName] 保留 $yearMonth 月备份: $($latestInMonth.Name)" -ForegroundColor Green
        }
    }
    
    Write-Host "📊 [$EditorName] 清理完成: 保留 $keptCount 个，删除 $deletedCount 个" -ForegroundColor Cyan
}

# 执行备份清理
Write-Host "`n🧹 开始执行备份清理策略..." -ForegroundColor Blue
Write-Host "📋 清理规则: 每月保留1个最新备份，只保留最近3个月" -ForegroundColor Gray

if ($backupCursor) {
    Clean-OldBackups -BackupRootPath $BackupPath -EditorName "Cursor"
}

if ($backupVSCode) {
    Clean-OldBackups -BackupRootPath $BackupPath -EditorName "VSCode"
}

Write-Host "`n🎉 备份任务完成！" -ForegroundColor Green
Write-Host "📋 扩展列表已保存，可在还原时下载扩展文件" -ForegroundColor Gray
Write-Host "`n🎉 备份和清理任务全部完成！" -ForegroundColor Green
Write-Host "📋 扩展列表已保存，可在还原时下载扩展文件" -ForegroundColor Gray
