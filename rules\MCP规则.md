---
alwaysApply: true
---

# MCP 智能调用规则

## 📊 MCP 工具调用决策流程

```mermaid
flowchart TD
    A[用户请求] --> A1{Vue3开发?<br/>组件/需求/规范}
    A1 -->|是| A2[智能读取Vue3规则<br/>检查状态后读取]
    A1 -->|否| B{复杂问题?<br/>多步骤/系统性/设计}

    A2 --> B
    B -->|是| C[sequential-thinking<br/>系统性分析]
    B -->|否| D{前端Bug?<br/>错误/性能/调试}

    %% 异常处理分支
    C -->|调用失败| C_ERR[手动分步分析<br/>问题拆解→逐步解决]
    C -->|调用成功| C1{需求改动?<br/>功能变更/迭代}
    C_ERR --> C1

    C1 -->|是| C2[feedback确认<br/>需求理解]
    C1 -->|否| D
    C2 --> D

    D -->|是| E[browser-tools<br/>诊断调试]
    D -->|否| F{网页抓取?<br/>URL/网页内容}

    E --> G{Git操作?<br/>版本控制}
    F -->|是| H[fetcher<br/>内容抓取]
    F -->|否| G

    %% 网络异常处理
    H -->|抓取失败| H_ERR[web_search备用<br/>或请用户提供]
    H -->|抓取成功| G
    H_ERR --> G

    G -->|是| I[git-mcp-server<br/>版本控制]
    G -->|否| J{查询文档?}

    I --> K[feedback确认<br/>操作前后]
    J -->|技术库| L[Context7<br/>技术文档]
    J -->|专业知识| M[wikipedia<br/>百科知识]
    J -->|否| N{需要记忆?<br/>明确要求记住}

    %% 文档查询异常处理
    L -->|查询失败| L_ERR[github-mcp搜索<br/>或基于经验分析]
    L -->|查询成功| N
    L_ERR --> N
    M --> N

    K --> O[执行操作]
    N -->|是| O1[memory-manager<br/>存储记忆]
    N -->|否| O2{需要确认?<br/>疑问/改动}

    O --> R[feedback总结<br/>结果确认]
    O1 --> O2
    O2 -->|是| P[feedback-enhanced<br/>沟通确认]
    O2 -->|否| Q[完成任务]

    %% 超时异常处理
    P -->|超时300s| P_ERR[智能推测方案<br/>标注请确认]
    P -->|正常响应| Q
    P_ERR --> Q
    R --> Q

```

## 🛠️ 工具调用矩阵

| 工具                    | 优先级 | 触发关键词                      | 适用场景          | 核心功能 | 调用时机                      |
| ----------------------- | ------ | ------------------------------- | ----------------- | -------- | ----------------------------- |
| **fetch_rules(Vue3)**   | P0     | Vue3/组件/规范/前端开发         | Vue3 项目开发     | 规范获取 | Vue3 开发首次智能读取         |
| **sequential-thinking** | P0     | 复杂/分析/设计/方案/架构/系统性 | 多步骤系统性问题  | 分析引擎 | 复杂问题必须先调用            |
| **feedback-enhanced**   | P0     | 疑问/确认/改动/澄清/需求变更    | 关键节点确认沟通  | 沟通桥梁 | 文件改动/需求改动前后必须调用 |
| **git-mcp-server**      | P0     | git/提交/分支/版本/仓库         | 版本控制操作      | 操作手臂 | 所有 Git 操作必须使用         |
| **browser-tools**       | P0     | bug/错误/调试/性能/前端/控制台  | 前端 Bug 调试诊断 | 诊断专家 | 前端 Bug 必须使用             |
| **fetcher**             | P0     | 抓取/网页/URL/fetch/爬取        | 网页内容抓取处理  | 抓取专家 | 网页抓取必须使用              |
| **Context7**            | P1     | 文档/API/教程/技术库            | 技术文档查询      | 文档查询 | 技术库查询优先使用            |
| **wikipedia**           | P1     | 百科/历史/科学/概念             | 专业知识查询      | 知识查询 | 非代码知识优先使用            |
| **memory-manager**      | P2     | 记住/保存/记录/存储/记忆        | 持久化记忆存储    | 记忆管理 | 用户明确要求记住时才使用      |

## ⚠️ 特殊规则

### 🎯 Vue3 开发规则

- **智能读取**: Vue3 项目开发时检查对话历史，未读取规则时调用 `fetch_rules(["Vue3开发规则"])`
- **状态管理**: 对话中首次读取后标记已读状态，避免重复调用
- **重读条件**: 新对话开始、用户明确要求重读、规则文件更新时重新读取
- **规范遵循**: 严格按照 Vue3 开发规范进行组件开发和代码编写
- **类型定义**: 必须包含完整的 TypeScript 类型定义和中文注释
- **需求变更**: 任何需求改动都必须通过 feedback-enhanced 确认理解

### 📋 需求改动处理规程

- **需求澄清**: 收到需求改动时必须先通过 feedback-enhanced 确认具体变更内容
- **影响评估**: 使用 sequential-thinking 分析需求变更对现有代码的影响
- **实施确认**: 改动前后都必须通过 feedback-enhanced 与用户确认

### 🚫 Git 操作强制约束

- ❌ **禁用**: `commit,add,push,pull` → 用户提交必须用 `yarn git:push`
- ✅ **必须**: 执行前通过 feedback-enhanced 确认
- 📝 **必须**: 提交后通过 feedback-enhanced 总结结果
- ⚠️ **危险操作**: reset --hard, clean -f, push --force → 必须详细说明风险并取得用户许可

### 📋 feedback-enhanced 必调用场景

- **疑问澄清**: 任何疑问时确保理解准确
- **文件改动**: 改动前后必须确认思路和结果
- **Bug 调试**: 调试前后确认策略和总结
- **危险操作**: 执行前进行风险警告确认
- **任务完成**: 完成后必须再次沟通确认，永远不主动断开对话，直到用户明确告诉结束

### 💾 memory-manager 记忆管理规则

- **明确指令**：只有用户明确提及"记住"、"保存"、"记录"、"存储"等关键词时才调用
- **主动原则**：必须用户主动要求，AI 不能自主决定记忆内容
- **内容确认**：记忆前必须确认要记忆的具体内容和标题
- **调用限制**：禁止在普通对话中主动调用，仅响应明确的记忆指令

### 🎯 避免过度调用

- 用户简短回复("是"、"好"、"可以") → 不再调用 feedback
- 收集信息后避免重复收集
- 新问题/新要求 → 重新调用工具
- sequential-thinking 后根据复杂度考虑调用 todo_tool 或者 "Generate a Tasklist for [任务], I'll review before execution"（3+步骤任务）
- **memory-manager**：仅当用户明确要求记忆时才调用，不得主动记忆

### 🚨 异常处理原则

#### 工具失败 → 立即替代

- `sequential-thinking` 失败 → 手动分步分析
- `fetcher` 失败 → `web_search` 或请用户提供
- `Context7` 失败 → `github-mcp` 或基于经验

#### 超时处理 → 智能推测

- `feedback-enhanced` 超时 300 秒 → 基于上下文推测，标注"请确认"

#### 用户沟通 → 简单直接

```
"主人，[工具名]暂时不可用，🤖小C用[替代方案]继续分析"
"主人，等待超时，🤖小C基于理解推测方案，请您确认"
```

## ✅ 快速检查清单

### P0 级核心检查

- [ ] Vue3 开发是否智能读取开发规则？
- [ ] 需求改动是否通过 feedback 确认理解？
- [ ] 复杂问题是否先调用 sequential-thinking？
- [ ] 前端 Bug 是否调用 browser-tools？
- [ ] Git 操作是否使用 git-mcp-server？
- [ ] 文件改动前后是否 feedback 确认？
- [ ] 危险操作是否详细说明风险？
- [ ] 工具调用失败时是否立即启用替代方案？

### P1 级优化检查

- [ ] 技术文档是否优先查 Context7？
- [ ] 专业知识是否优先查 wikipedia？
- [ ] sequential-thinking 后是否考虑 todo_tool 或者 "Generate a Tasklist for [任务], I'll review before execution"（3+步骤任务）？

### P2 级辅助检查

- [ ] 用户是否明确要求记忆？仅在明确指令下使用 memory-manager

## 💡 核心理念

- **安全第一**: 确认优于假设，质量优于数量
- **工具定位**: sequential-thinking(分析大脑) + feedback(沟通桥梁) + git-mcp-server(操作手臂)
- **用户主导**: 对话结束权交给用户，AI 不能主动结束对话
