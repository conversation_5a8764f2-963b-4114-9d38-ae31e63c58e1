{"name": "ai-coding-config", "version": "1.0.0", "description": "AI智能编程配置工具集，集成MCP工具链、Cursor编辑器配置、Vue3开发规范和自动化脚本", "main": "index.js", "repository": "*************:cjh-1996/cursor-rule.git", "author": "cjh", "scripts": {"git:push": "git add -A && git-pro commit && git pull && git push", "install:mcp": "powershell -ExecutionPolicy Bypass -File ./scripts/install-mcp-plugins.ps1", "backup:editors": "powershell -ExecutionPolicy Bypass -File ./scripts/backup-editors-settings.ps1", "restore:editors": "powershell -ExecutionPolicy Bypass -File ./scripts/restore-editors-settings.ps1", "build:rules": "ts-node ./rules/build/index.ts"}, "dependencies": {"@cjh0/git-pro": "latest", "inquirer": "^12.8.2"}, "keywords": ["mcp", "cursor", "development-rules", "vue3"], "license": "MIT", "devDependencies": {"@types/inquirer": "^9.0.8", "@types/node": "^24.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}