import * as fs from "fs";
import * as path from "path";
import { FileMetadata, CopyResult } from "./types";

/**
 * 批量复制文件配置接口
 */
export interface CopyFilesConfig {
  sourceDir: string;
  targetDir: string;
  fileExtension: string;
  metadataMap?: Record<string, FileMetadata>;
  metadataFormat?: 'yaml' | 'json' | 'none';
  converter?: (content: string) => string;
}

/**
 * 文件操作工具类
 */
export class FileUtils {
  /**
   * 解析文件的YAML前言
   */
  static parseYamlFrontMatter(content: string): { [key: string]: any } | null {
    if (!content.startsWith('---')) {
      return null;
    }

    const endIndex = content.indexOf('---', 3);
    if (endIndex === -1) {
      return null;
    }

    const yamlHeader = content.substring(3, endIndex).trim();
    const metadata: { [key: string]: any } = {};

    // 简单解析YAML头部
    const lines = yamlHeader.split('\n');
    for (const line of lines) {
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const key = line.substring(0, colonIndex).trim();
        let value: any = line.substring(colonIndex + 1).trim();

        // 移除引号
        if ((value.startsWith('"') && value.endsWith('"')) ||
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1);
        }

        // 转换布尔值
        if (value === 'true') value = true;
        if (value === 'false') value = false;

        metadata[key] = value;
      }
    }

    return metadata;
  }

  /**
   * 检查文件是否应该被忽略
   */
  static shouldIgnoreFile(filePath: string): boolean {
    try {
      const content = fs.readFileSync(filePath, "utf-8");
      const frontMatter = this.parseYamlFrontMatter(content);
      
      return frontMatter?.ignore === true;
    } catch (error) {
      // 如果读取文件失败，不忽略该文件
      return false;
    }
  }
  /**
   * 确保目录存在
   */
  static ensureDirectory(dir: string): void {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 创建目录: ${dir}`);
    }
  }

  /**
   * 清理目录
   */
  static cleanDirectory(dir: string): void {
    console.log(`🔍 检查目录: ${dir}`);
    if (fs.existsSync(dir)) {
      console.log(`🗑️ 删除现有目录: ${dir}`);
      try {
        fs.rmSync(dir, { recursive: true, force: true });
        console.log(`✅ 成功删除现有目录`);
      } catch (error) {
        console.log(`⚠️ 删除目录失败: ${(error as Error).message}`);
        throw error;
      }
    } else {
      console.log(`📁 目录不存在，无需清理`);
    }
  }

  /**
   * 复制文件并添加元数据
   */
  static copyFileWithMetadata(
    sourcePath: string,
    targetPath: string,
    metadata?: FileMetadata,
    metadataFormat: 'yaml' | 'json' | 'none' = 'yaml'
  ): void {
    // 读取源文件内容
    let content = fs.readFileSync(sourcePath, "utf-8");

    // 添加元数据头部
    if (metadata && metadataFormat !== 'none') {
      const header = this.generateMetadataHeader(metadata, metadataFormat);

      // 检查文件是否已经有头部信息
      if (!content.startsWith("---") && metadataFormat === 'yaml') {
        content = header + content;
      } else if (!content.startsWith("/*") && metadataFormat === 'json') {
        content = header + content;
      }
    }

    // 写入目标文件
    fs.writeFileSync(targetPath, content, "utf-8");
  }



  /**
   * 生成元数据头部
   */
  private static generateMetadataHeader(
    metadata: FileMetadata,
    format: 'yaml' | 'json'
  ): string {
    if (format === 'yaml') {
      const yamlLines = ['---'];
      Object.entries(metadata).forEach(([key, value]) => {
        if (typeof value === 'string') {
          yamlLines.push(`${key}: "${value}"`);
        } else {
          yamlLines.push(`${key}: ${value}`);
        }
      });
      yamlLines.push('---', '', '');
      return yamlLines.join('\n');
    } else if (format === 'json') {
      return `/*\n${JSON.stringify(metadata, null, 2)}\n*/\n\n`;
    }
    return '';
  }

  /**
   * 批量复制文件
   */
  static copyFiles(config: CopyFilesConfig): CopyResult {
    const {
      sourceDir,
      targetDir,
      fileExtension,
      metadataMap = {},
      metadataFormat = 'yaml',
      converter
    } = config;
    if (!fs.existsSync(sourceDir)) {
      throw new Error(`源目录不存在: ${sourceDir}`);
    }

    const files = fs.readdirSync(sourceDir);
    let copiedCount = 0;
    let skippedCount = 0;
    let ignoredCount = 0;
    const errors: string[] = [];

    for (const file of files) {
      const sourcePath = path.join(sourceDir, file);
      
      // 只处理文件，跳过目录
      if (!fs.statSync(sourcePath).isFile()) {
        skippedCount++;
        continue;
      }

      // 检查文件是否应该被忽略
      if (this.shouldIgnoreFile(sourcePath)) {
        console.log(`🚫 忽略文件: ${file} (ignore: true)`);
        ignoredCount++;
        continue;
      }

      try {
        // 生成目标文件名（可能需要更改扩展名）
        const baseName = path.parse(file).name;
        const targetFileName = `${baseName}${fileExtension}`;
        const targetPath = path.join(targetDir, targetFileName);

        // 获取文件的元数据
        const metadata = metadataMap[file];

        // 复制文件
        if (converter) {
          // 使用自定义转换器
          const content = fs.readFileSync(sourcePath, "utf-8");
          const convertedContent = converter(content);
          fs.writeFileSync(targetPath, convertedContent, "utf-8");
        } else {
          // 使用标准复制方法
          this.copyFileWithMetadata(sourcePath, targetPath, metadata, metadataFormat);
        }
        
        console.log(`📄 复制文件: ${file} → ${targetFileName}`);
        copiedCount++;
      } catch (error) {
        const errorMsg = `复制文件 ${file} 失败: ${(error as Error).message}`;
        errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    return { copiedCount, skippedCount, ignoredCount, errors };
  }

  /**
   * 更新 .gitignore 文件
   */
  static updateGitignore(projectPath: string, rules: string[]): void {
    const gitignorePath = path.join(projectPath, ".gitignore");

    try {
      let existingContent = "";
      let needsUpdate = false;

      // 读取现有的 .gitignore 内容
      if (fs.existsSync(gitignorePath)) {
        existingContent = fs.readFileSync(gitignorePath, "utf-8");
        console.log(`📄 读取现有 .gitignore 文件`);
      } else {
        console.log(`📄 目标项目没有 .gitignore 文件，将创建新文件`);
        needsUpdate = true;
      }

      // 检查是否包含所需的规则
      const missingRules: string[] = [];
      for (const rule of rules) {
        if (!existingContent.includes(rule)) {
          missingRules.push(rule);
          needsUpdate = true;
        }
      }

      if (needsUpdate) {
        let newContent = existingContent;

        if (missingRules.length > 0) {
          // 如果文件不为空且不以换行符结尾，添加换行符
          if (newContent && !newContent.endsWith('\n')) {
            newContent += '\n';
          }

          // 添加缺失的规则
          newContent += '\n' + missingRules.join('\n') + '\n';
        }

        // 写入更新后的内容
        fs.writeFileSync(gitignorePath, newContent, "utf-8");
        console.log(`✅ 已更新 .gitignore 文件，添加了 ${missingRules.length} 条规则`);
      } else {
        console.log(`✅ .gitignore 文件已包含所需规则，无需更新`);
      }
    } catch (error) {
      console.log(`⚠️ 更新 .gitignore 失败: ${(error as Error).message}`);
    }
  }
}
