{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "ts-node": {"compilerOptions": {"module": "commonjs"}}, "include": ["build-rule/**/*", "scripts/**/*", "src/**/*", "rules/build/common", "rules/build/targets", "rules/build/index.ts"], "exclude": ["node_modules", "**/*.test.ts"]}