import * as path from "path";
import { TargetConfig, FileMetadata } from "../common/types";
import { FileUtils } from "../common/file-utils";

/**
 * Augment 目标构建配置
 */
export class AugmentTarget {
  static getConfig(): TargetConfig {
    return {
      name: 'Augment',
      outputDir: '.augment/rules',
      fileExtension: '.md',
      metadataFormat: 'yaml',
      description: 'Augment AI 编辑器规则目录'
    };
  }



  /**
   * 构建到指定项目
   */
  static async buildToProject(
    sourceDir: string,
    projectPath: string,
    isCurrentProject: boolean = false
  ): Promise<void> {
    const config = this.getConfig();
    const targetDir = path.join(projectPath, config.outputDir);
    const parentDir = path.dirname(targetDir);

    console.log(`📦 构建 ${config.name} 目标到: ${projectPath}`);

    // 统一逻辑：无论是否当前项目，都先清理现有目录
    FileUtils.cleanDirectory(parentDir);

    // 创建目录
    FileUtils.ensureDirectory(parentDir);
    FileUtils.ensureDirectory(targetDir);

    // 复制文件（从源文件的Cursor头部转换为Augment格式）
    const result = FileUtils.copyFiles({
      sourceDir,
      targetDir,
      fileExtension: config.fileExtension,
      converter: this.convertCursorToAugment
    });

    console.log(`✅ ${config.name} 构建完成！共复制 ${result.copiedCount} 个文件${result.ignoredCount > 0 ? `，忽略 ${result.ignoredCount} 个文件` : ''}`);

    // 如果不是当前项目，更新 .gitignore
    if (!isCurrentProject) {
      this.updateGitignore(projectPath);
    }
  }

  /**
   * 更新 .gitignore 文件
   */
  private static updateGitignore(projectPath: string): void {
    const rules = [
      "# Ignore all dotfiles except .vscode",
      ".*",
      "!.vscode/",
      "!.gitignore"
    ];

    FileUtils.updateGitignore(projectPath, rules);
  }

  /**
   * 将Cursor格式转换为Augment格式
   */
  private static convertCursorToAugment(content: string): string {
    let cleanContent = content;
    let cursorMetadata: any = {};

    // 解析现有的Cursor格式头部
    if (content.startsWith('---')) {
      const endIndex = content.indexOf('---', 3);
      if (endIndex !== -1) {
        const yamlHeader = content.substring(3, endIndex).trim();
        cleanContent = content.substring(endIndex + 3).replace(/^\n+/, '');

        // 简单解析YAML头部
        const lines = yamlHeader.split('\n');
        for (const line of lines) {
          const colonIndex = line.indexOf(':');
          if (colonIndex > 0) {
            const key = line.substring(0, colonIndex).trim();
            let value: any = line.substring(colonIndex + 1).trim();

            // 移除引号
            if ((value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))) {
              value = value.slice(1, -1);
            }

            // 转换布尔值
            if (value === 'true') value = true;
            if (value === 'false') value = false;

            cursorMetadata[key] = value;
          }
        }
      }
    }

    // 将Cursor格式转换为Augment格式
    const augmentMetadata: FileMetadata = {};

    if (cursorMetadata.alwaysApply === true) {
      augmentMetadata.type = "always_apply";
    } else if (cursorMetadata.alwaysApply === false) {
      augmentMetadata.type = "agent_requested";
      if (cursorMetadata.description) {
        augmentMetadata.description = cursorMetadata.description;
      }
    }

    // 保留ignore字段
    if (cursorMetadata.ignore !== undefined) {
      augmentMetadata.ignore = cursorMetadata.ignore;
    }

    // 添加Augment格式头部
    if (Object.keys(augmentMetadata).length > 0) {
      const yamlLines = ['---'];
      Object.entries(augmentMetadata).forEach(([key, value]) => {
        if (typeof value === 'string') {
          yamlLines.push(`${key}: "${value}"`);
        } else {
          yamlLines.push(`${key}: ${value}`);
        }
      });
      yamlLines.push('---', '', '');
      const header = yamlLines.join('\n');
      cleanContent = header + cleanContent;
    }

    return cleanContent;
  }


}
