import * as path from "path";
import { TargetConfig, FileMetadata } from "../common/types";
import { FileUtils } from "../common/file-utils";

/**
 * Copilot 目标构建配置
 */
export class CopilotTarget {
  static getConfig(): TargetConfig {
    return {
      name: 'Copilot',
      outputDir: '.github/instructions',
      fileExtension: '.instructions.md',
      metadataFormat: 'none', // Copilot 不需要元数据头部
      description: 'GitHub Copilot 指令目录'
    };
  }

  /**
   * 转换 Cursor 格式到 Copilot 格式
   * 保留 description 字段的 YAML 前言格式，并添加 applyTo: "**"
   */
  private static convertCursorToCopilot(content: string): string {
    let cleanContent = content;
    let description = '';
    
    if (content.startsWith('---')) {
      const endIndex = content.indexOf('---', 3);
      if (endIndex !== -1) {
        const yamlHeader = content.substring(3, endIndex).trim();
        cleanContent = content.substring(endIndex + 3).replace(/^\n+/, '');
        
        // 解析 YAML 头部，提取 description
        const lines = yamlHeader.split('\n');
        for (const line of lines) {
          const colonIndex = line.indexOf(':');
          if (colonIndex > 0) {
            const key = line.substring(0, colonIndex).trim();
            let value: any = line.substring(colonIndex + 1).trim();
            
            // 保持原始值格式（包括引号）
            if (key === 'description' && value) {
              description = value;
            }
          }
        }
      }
    }
    
    // 创建 Copilot 格式的 YAML 前言，始终包含 applyTo: "**"
    let yamlFrontMatter = `---\napplyTo: "**"`;
    
    if (description) {
      yamlFrontMatter += `\ndescription: ${description}`;
    }
    
    yamlFrontMatter += `\n---\n\n`;
    
    return yamlFrontMatter + cleanContent.trim();
  }

  /**
   * 构建到指定项目
   */
  static async buildToProject(
    sourceDir: string,
    projectPath: string,
    isCurrentProject: boolean = false
  ): Promise<void> {
    const config = this.getConfig();
    const targetDir = path.join(projectPath, config.outputDir);
    const parentDir = path.dirname(targetDir);

    console.log(`📦 构建 ${config.name} 目标到: ${projectPath}`);

    // 统一逻辑：无论是否当前项目，都先清理现有目录
    FileUtils.cleanDirectory(parentDir);

    // 创建目录
    FileUtils.ensureDirectory(parentDir);
    FileUtils.ensureDirectory(targetDir);

    // 复制文件（从源文件的Cursor头部转换为Copilot格式）
    const result = FileUtils.copyFiles({
      sourceDir,
      targetDir,
      fileExtension: config.fileExtension,
      converter: this.convertCursorToCopilot
    });

    console.log(`✅ ${config.name} 构建完成！共复制 ${result.copiedCount} 个文件${result.ignoredCount > 0 ? `，忽略 ${result.ignoredCount} 个文件` : ''}`);

    if (result.errors.length > 0) {
      console.log(`⚠️  构建过程中发生错误:`);
      result.errors.forEach(error => console.log(`   ${error}`));
    }
  }

  /**
   * 获取特定于 Copilot 的输出文件名
   * 将文件名转换为 .instructions.md 格式
   */
  static getOutputFileName(sourceFileName: string): string {
    const baseName = path.parse(sourceFileName).name;
    return `${baseName}.instructions.md`;
  }
}
